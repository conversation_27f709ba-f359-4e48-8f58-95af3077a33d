"""
Pro Trader RL 主训练脚本
训练Buy Knowledge RL和Sell Knowledge RL智能体
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
import json
from datetime import datetime
from tqdm import tqdm
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

# 导入自定义模块
from config import *
from data_preprocessing import DataPreprocessor
from rl_environments import BuyKnowledgeRLEnv, SellKnowledgeRLEnv, StopLossRule
from ppo_agent import BuyKnowledgeRLAgent, SellKnowledgeRLAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(LOG_DIR, f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ProTraderRLTrainer:
    """
    Pro Trader RL训练器
    负责训练Buy Knowledge RL和Sell Knowledge RL
    """
    
    def __init__(self):
        """初始化训练器"""
        logger.info("="*50)
        logger.info("Pro Trader RL 训练器初始化")
        logger.info("="*50)
        
        # 创建数据预处理器
        self.preprocessor = DataPreprocessor()
        
        # 加载和处理数据
        logger.info("加载和处理数据...")
        self.data = self.preprocessor.process_all_data()
        
        # 划分训练集和验证集
        self._split_data()
        
        # 创建智能体
        self.buy_agent = BuyKnowledgeRLAgent()
        self.sell_agent = SellKnowledgeRLAgent()
        
        # 创建环境
        self.buy_env_train = BuyKnowledgeRLEnv(self.train_data, mode='train')
        self.buy_env_val = BuyKnowledgeRLEnv(self.val_data, mode='test')
        
        self.sell_env_train = SellKnowledgeRLEnv(self.train_data, mode='train')
        self.sell_env_val = SellKnowledgeRLEnv(self.val_data, mode='test')
        
        # 训练历史
        self.training_history = {
            'buy_agent': {
                'rewards': [],
                'losses': [],
                'accuracies': [],
                'val_rewards': [],
                'val_accuracies': []
            },
            'sell_agent': {
                'rewards': [],
                'losses': [],
                'accuracies': [],
                'val_rewards': [],
                'val_accuracies': []
            }
        }
        
        # 最佳模型性能
        self.best_buy_accuracy = 0
        self.best_sell_accuracy = 0
        
        logger.info("训练器初始化完成")
    
    def _split_data(self):
        """划分训练和验证数据"""
        # 根据日期划分
        train_start = pd.to_datetime(TRAIN_START_DATE)
        train_end = pd.to_datetime(TRAIN_END_DATE)
        val_start = pd.to_datetime(VAL_START_DATE)
        val_end = pd.to_datetime(VAL_END_DATE)

        # 训练集：使用开始和结束日期进行过滤
        self.train_data = self.data[
            (self.data['trade_date'] >= train_start) &
            (self.data['trade_date'] <= train_end)
        ]

        # 验证集：使用开始和结束日期进行过滤
        self.val_data = self.data[
            (self.data['trade_date'] >= val_start) &
            (self.data['trade_date'] <= val_end)
        ]

        logger.info(f"数据划分完成：")
        logger.info(f"  训练集：{len(self.train_data)} 条 ({TRAIN_START_DATE} 至 {TRAIN_END_DATE})")
        logger.info(f"  验证集：{len(self.val_data)} 条 ({VAL_START_DATE} 至 {VAL_END_DATE})")
        logger.info(f"  训练集日期范围：{self.train_data['trade_date'].min()} 至 {self.train_data['trade_date'].max()}")
        logger.info(f"  验证集日期范围：{self.val_data['trade_date'].min()} 至 {self.val_data['trade_date'].max()}")
    
    def train_buy_knowledge_rl(self, n_episodes: int = 1000):
        """
        训练Buy Knowledge RL智能体
        
        Args:
            n_episodes: 训练回合数
        """
        logger.info("="*50)
        logger.info("开始训练Buy Knowledge RL")
        logger.info("="*50)
        
        for episode in tqdm(range(n_episodes), desc="训练Buy Knowledge RL"):
            # 重置环境
            state = self.buy_env_train.reset()
            episode_reward = 0
            episode_correct = 0
            episode_steps = 0
            
            done = False
            while not done:
                # 选择动作
                action, log_prob, value = self.buy_agent.select_action(state)
                
                # 执行动作
                next_state, reward, done, info = self.buy_env_train.step(action)
                
                # 存储经验
                self.buy_agent.store_transition(state, action, reward, value, log_prob, done)
                
                # 更新统计
                episode_reward += reward
                episode_correct += info['correct']
                episode_steps += 1
                
                state = next_state
                
                # 定期更新策略
                if len(self.buy_agent.states) >= PPO_CONFIG['n_steps']:
                    losses = self.buy_agent.update(
                        batch_size=PPO_CONFIG['batch_size'],
                        n_epochs=PPO_CONFIG['n_epochs']
                    )
                    if losses:
                        self.training_history['buy_agent']['losses'].append(losses)
            
            # 记录训练统计
            accuracy = episode_correct / max(1, episode_steps)
            self.training_history['buy_agent']['rewards'].append(episode_reward)
            self.training_history['buy_agent']['accuracies'].append(accuracy)
            
            # 定期验证和保存
            if (episode + 1) % 100 == 0:
                val_reward, val_accuracy = self._validate_buy_agent()
                self.training_history['buy_agent']['val_rewards'].append(val_reward)
                self.training_history['buy_agent']['val_accuracies'].append(val_accuracy)
                
                logger.info(f"Episode {episode+1}: "
                          f"Train Reward={episode_reward:.2f}, Accuracy={accuracy:.4f}, "
                          f"Val Reward={val_reward:.2f}, Val Accuracy={val_accuracy:.4f}")
                
                # 保存最佳模型
                if val_accuracy > self.best_buy_accuracy:
                    self.best_buy_accuracy = val_accuracy
                    model_path = os.path.join(MODEL_DIR, 'buy_knowledge_rl_best.pth')
                    self.buy_agent.save(model_path)
                    logger.info(f"保存最佳Buy Knowledge RL模型，准确率: {val_accuracy:.4f}")
        
        # 保存最终模型
        final_model_path = os.path.join(MODEL_DIR, 'buy_knowledge_rl_final.pth')
        self.buy_agent.save(final_model_path)
        logger.info("Buy Knowledge RL训练完成")
    
    def train_sell_knowledge_rl(self, n_episodes: int = 1000):
        """
        训练Sell Knowledge RL智能体
        
        Args:
            n_episodes: 训练回合数
        """
        logger.info("="*50)
        logger.info("开始训练Sell Knowledge RL")
        logger.info("="*50)
        
        for episode in tqdm(range(n_episodes), desc="训练Sell Knowledge RL"):
            # 重置环境
            state = self.sell_env_train.reset()
            episode_reward = 0
            episode_steps = 0
            optimal_sells = 0
            
            done = False
            while not done:
                # 选择动作
                action, log_prob, value = self.sell_agent.select_action(state)
                
                # 执行动作
                next_state, reward, done, info = self.sell_env_train.step(action)
                
                # 存储经验
                self.sell_agent.store_transition(state, action, reward, value, log_prob, done)
                
                # 更新统计
                episode_reward += reward
                episode_steps += 1
                if reward > 1.0:  # 相对奖励>1表示在高收益点卖出
                    optimal_sells += 1
                
                state = next_state
                
                # 定期更新策略
                if len(self.sell_agent.states) >= PPO_CONFIG['n_steps']:
                    losses = self.sell_agent.update(
                        batch_size=PPO_CONFIG['batch_size'],
                        n_epochs=PPO_CONFIG['n_epochs']
                    )
                    if losses:
                        self.training_history['sell_agent']['losses'].append(losses)
            
            # 记录训练统计
            accuracy = optimal_sells / max(1, episode_steps)
            self.training_history['sell_agent']['rewards'].append(episode_reward)
            self.training_history['sell_agent']['accuracies'].append(accuracy)
            
            # 定期验证和保存
            if (episode + 1) % 100 == 0:
                val_reward, val_accuracy = self._validate_sell_agent()
                self.training_history['sell_agent']['val_rewards'].append(val_reward)
                self.training_history['sell_agent']['val_accuracies'].append(val_accuracy)
                
                logger.info(f"Episode {episode+1}: "
                          f"Train Reward={episode_reward:.2f}, Optimal Sells={optimal_sells}, "
                          f"Val Reward={val_reward:.2f}, Val Accuracy={val_accuracy:.4f}")
                
                # 保存最佳模型
                if val_accuracy > self.best_sell_accuracy:
                    self.best_sell_accuracy = val_accuracy
                    model_path = os.path.join(MODEL_DIR, 'sell_knowledge_rl_best.pth')
                    self.sell_agent.save(model_path)
                    logger.info(f"保存最佳Sell Knowledge RL模型，准确率: {val_accuracy:.4f}")
        
        # 保存最终模型
        final_model_path = os.path.join(MODEL_DIR, 'sell_knowledge_rl_final.pth')
        self.sell_agent.save(final_model_path)
        logger.info("Sell Knowledge RL训练完成")
    
    def _validate_buy_agent(self) -> Tuple[float, float]:
        """
        验证Buy Knowledge RL智能体
        
        Returns:
            (平均奖励, 准确率)
        """
        total_reward = 0
        total_correct = 0
        total_steps = 0
        n_episodes = 10
        
        for _ in range(n_episodes):
            state = self.buy_env_val.reset()
            done = False
            
            while not done:
                action, _, _ = self.buy_agent.select_action(state, deterministic=True)
                state, reward, done, info = self.buy_env_val.step(action)
                
                total_reward += reward
                total_correct += info['correct']
                total_steps += 1
        
        avg_reward = total_reward / n_episodes
        accuracy = total_correct / max(1, total_steps)
        
        return avg_reward, accuracy
    
    def _validate_sell_agent(self) -> Tuple[float, float]:
        """
        验证Sell Knowledge RL智能体
        
        Returns:
            (平均奖励, 准确率)
        """
        total_reward = 0
        optimal_sells = 0
        total_episodes = 0
        n_episodes = 10
        
        for _ in range(n_episodes):
            state = self.sell_env_val.reset()
            done = False
            episode_reward = 0
            
            while not done:
                action, _, _ = self.sell_agent.select_action(state, deterministic=True)
                state, reward, done, info = self.sell_env_val.step(action)
                
                episode_reward += reward
                if reward > 1.0:
                    optimal_sells += 1
            
            total_reward += episode_reward
            total_episodes += 1
        
        avg_reward = total_reward / n_episodes
        accuracy = optimal_sells / max(1, total_episodes)
        
        return avg_reward, accuracy
    
    def train_all(self):
        """训练所有智能体"""
        # 训练Buy Knowledge RL
        self.train_buy_knowledge_rl(n_episodes=TRAINING_CONFIG['total_timesteps'] // 1000)
        
        # 训练Sell Knowledge RL
        self.train_sell_knowledge_rl(n_episodes=TRAINING_CONFIG['total_timesteps'] // 1000)
        
        # 保存训练历史
        self.save_training_history()
        
        # 绘制训练曲线
        self.plot_training_curves()
        
        logger.info("="*50)
        logger.info("所有训练完成！")
        logger.info(f"最佳Buy Knowledge RL准确率: {self.best_buy_accuracy:.4f}")
        logger.info(f"最佳Sell Knowledge RL准确率: {self.best_sell_accuracy:.4f}")
        logger.info("="*50)
    
    def save_training_history(self):
        """保存训练历史"""
        history_path = os.path.join(RESULTS_DIR, f'training_history_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        # 转换numpy数组为列表
        history_to_save = {}
        for agent_name, agent_history in self.training_history.items():
            history_to_save[agent_name] = {}
            for key, value in agent_history.items():
                if isinstance(value, list) and len(value) > 0:
                    if isinstance(value[0], dict):
                        history_to_save[agent_name][key] = value
                    else:
                        history_to_save[agent_name][key] = [float(v) for v in value]
                else:
                    history_to_save[agent_name][key] = value
        
        with open(history_path, 'w') as f:
            json.dump(history_to_save, f, indent=4)
        
        logger.info(f"训练历史已保存到: {history_path}")
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Buy Knowledge RL奖励曲线
        axes[0, 0].plot(self.training_history['buy_agent']['rewards'], label='Train', alpha=0.7)
        if self.training_history['buy_agent']['val_rewards']:
            x_val = np.arange(99, len(self.training_history['buy_agent']['rewards']), 100)
            axes[0, 0].plot(x_val, self.training_history['buy_agent']['val_rewards'], 
                          label='Validation', marker='o')
        axes[0, 0].set_title('Buy Knowledge RL - Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Reward')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Buy Knowledge RL准确率曲线
        axes[0, 1].plot(self.training_history['buy_agent']['accuracies'], label='Train', alpha=0.7)
        if self.training_history['buy_agent']['val_accuracies']:
            x_val = np.arange(99, len(self.training_history['buy_agent']['accuracies']), 100)
            axes[0, 1].plot(x_val, self.training_history['buy_agent']['val_accuracies'], 
                          label='Validation', marker='o')
        axes[0, 1].set_title('Buy Knowledge RL - Accuracy')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # Sell Knowledge RL奖励曲线
        axes[1, 0].plot(self.training_history['sell_agent']['rewards'], label='Train', alpha=0.7)
        if self.training_history['sell_agent']['val_rewards']:
            x_val = np.arange(99, len(self.training_history['sell_agent']['rewards']), 100)
            axes[1, 0].plot(x_val, self.training_history['sell_agent']['val_rewards'], 
                          label='Validation', marker='o')
        axes[1, 0].set_title('Sell Knowledge RL - Rewards')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Reward')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # Sell Knowledge RL准确率曲线
        axes[1, 1].plot(self.training_history['sell_agent']['accuracies'], label='Train', alpha=0.7)
        if self.training_history['sell_agent']['val_accuracies']:
            x_val = np.arange(99, len(self.training_history['sell_agent']['accuracies']), 100)
            axes[1, 1].plot(x_val, self.training_history['sell_agent']['val_accuracies'], 
                          label='Validation', marker='o')
        axes[1, 1].set_title('Sell Knowledge RL - Optimal Sell Rate')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Rate')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        # 保存图表
        plot_path = os.path.join(RESULTS_DIR, f'training_curves_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
        plt.savefig(plot_path, dpi=300)
        plt.show()
        
        logger.info(f"训练曲线已保存到: {plot_path}")


def main():
    """主函数"""
    logger.info("="*60)
    logger.info("Pro Trader RL 训练程序启动")
    logger.info(f"Python路径: {sys.executable}")
    logger.info(f"工作目录: {os.getcwd()}")
    logger.info(f"配置文件: {__file__}")
    logger.info("="*60)
    
    # 设置随机种子
    np.random.seed(RANDOM_SEED)
    
    # 创建必要的目录
    os.makedirs(MODEL_DIR, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    try:
        # 创建训练器
        trainer = ProTraderRLTrainer()
        
        # 开始训练
        trainer.train_all()
        
        logger.info("训练程序正常结束")
        
    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    main()