"""
PPO算法实现
基于论文的网络结构和超参数配置
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.distributions import Categorical
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from config import *

logger = logging.getLogger(__name__)

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() and USE_GPU else "cpu")
logger.info(f"使用设备: {device}")


class PolicyNetwork(nn.Module):
    """
    策略网络（Actor-Critic架构）
    根据论文第7-8页的网络结构
    """
    
    def __init__(self, input_dim: int, hidden_layers: List[int], output_dim: int):
        """
        初始化策略网络
        
        Args:
            input_dim: 输入维度
            hidden_layers: 隐藏层维度列表
            output_dim: 输出维度（动作数量）
        """
        super(PolicyNetwork, self).__init__()
        
        # 构建网络层
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_layers[:-1]:  # 不包括最后一层
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.ReLU())
            prev_dim = hidden_dim
        
        # 共享层
        self.shared = nn.Sequential(*layers)
        
        # Actor头（策略）
        self.actor = nn.Linear(prev_dim, output_dim)
        
        # Critic头（价值函数）
        self.critic = nn.Linear(prev_dim, 1)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.orthogonal_(m.weight, gain=np.sqrt(2))
                nn.init.constant_(m.bias, 0.0)
        
        # Actor最后一层使用较小的初始化
        nn.init.orthogonal_(self.actor.weight, gain=0.01)
        nn.init.constant_(self.actor.bias, 0.0)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入状态
        
        Returns:
            (动作概率分布, 状态价值)
        """
        shared_features = self.shared(x)
        
        # 计算动作概率
        action_logits = self.actor(shared_features)
        action_probs = F.softmax(action_logits, dim=-1)
        
        # 计算状态价值
        state_value = self.critic(shared_features)
        
        return action_probs, state_value


class PPOAgent:
    """
    PPO智能体
    实现Proximal Policy Optimization算法
    """
    
    def __init__(self, 
                 input_dim: int,
                 hidden_layers: List[int],
                 output_dim: int,
                 learning_rate: float = 0.0001,
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95,
                 clip_range: float = 0.2,
                 value_coef: float = 0.5,
                 entropy_coef: float = 0.01,
                 max_grad_norm: float = 0.5):
        """
        初始化PPO智能体
        
        Args:
            input_dim: 输入维度
            hidden_layers: 隐藏层配置
            output_dim: 输出维度
            learning_rate: 学习率
            gamma: 折扣因子
            gae_lambda: GAE lambda参数
            clip_range: PPO裁剪范围
            value_coef: 价值损失系数
            entropy_coef: 熵正则化系数
            max_grad_norm: 梯度裁剪阈值
        """
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_range = clip_range
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        
        # 创建策略网络
        self.policy = PolicyNetwork(input_dim, hidden_layers, output_dim).to(device)
        
        # 创建优化器
        self.optimizer = optim.Adam(self.policy.parameters(), lr=learning_rate)
        
        # 经验缓冲区
        self.states = []
        self.actions = []
        self.rewards = []
        self.values = []
        self.log_probs = []
        self.dones = []
        
        logger.info(f"PPO智能体初始化完成: 输入={input_dim}, 隐藏层={hidden_layers}, 输出={output_dim}")
    
    def select_action(self, state: np.ndarray, deterministic: bool = False) -> Tuple[int, float, float]:
        """
        选择动作
        
        Args:
            state: 当前状态
            deterministic: 是否使用确定性策略
        
        Returns:
            (动作, 动作对数概率, 状态价值)
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(device)
        
        with torch.no_grad():
            action_probs, state_value = self.policy(state_tensor)
            
            if deterministic:
                action = action_probs.argmax(dim=-1)
            else:
                dist = Categorical(action_probs)
                action = dist.sample()
            
            log_prob = torch.log(action_probs[0, action])
        
        return action.item(), log_prob.item(), state_value.item()
    
    def store_transition(self, state: np.ndarray, action: int, reward: float, 
                        value: float, log_prob: float, done: bool):
        """
        存储经验
        
        Args:
            state: 状态
            action: 动作
            reward: 奖励
            value: 价值估计
            log_prob: 动作对数概率
            done: 是否结束
        """
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.values.append(value)
        self.log_probs.append(log_prob)
        self.dones.append(done)
    
    def compute_gae(self, next_value: float) -> np.ndarray:
        """
        计算广义优势估计（GAE）
        
        Args:
            next_value: 下一个状态的价值
        
        Returns:
            优势估计数组
        """
        advantages = []
        advantage = 0
        
        for t in reversed(range(len(self.rewards))):
            if t == len(self.rewards) - 1:
                next_value_t = next_value
            else:
                next_value_t = self.values[t + 1]
            
            delta = self.rewards[t] + self.gamma * next_value_t * (1 - self.dones[t]) - self.values[t]
            advantage = delta + self.gamma * self.gae_lambda * (1 - self.dones[t]) * advantage
            advantages.insert(0, advantage)
        
        return np.array(advantages)
    
    def update(self, batch_size: int = 64, n_epochs: int = 10) -> Dict[str, float]:
        """
        更新策略
        
        Args:
            batch_size: 批次大小
            n_epochs: 训练轮数
        
        Returns:
            训练损失字典
        """
        if len(self.states) == 0:
            return {}
        
        # 转换为张量
        states = torch.FloatTensor(np.array(self.states)).to(device)
        actions = torch.LongTensor(self.actions).to(device)
        old_log_probs = torch.FloatTensor(self.log_probs).to(device)
        
        # 计算优势和回报
        advantages = self.compute_gae(0)
        advantages = torch.FloatTensor(advantages).to(device)
        returns = advantages + torch.FloatTensor(self.values).to(device)
        
        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # 训练统计
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy_loss = 0
        update_count = 0
        
        # 多轮训练
        for epoch in range(n_epochs):
            # 打乱数据
            indices = np.random.permutation(len(states))
            
            for start_idx in range(0, len(states), batch_size):
                end_idx = min(start_idx + batch_size, len(states))
                batch_indices = indices[start_idx:end_idx]
                
                batch_states = states[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]
                
                # 前向传播
                action_probs, state_values = self.policy(batch_states)
                dist = Categorical(action_probs)
                
                # 计算新的动作对数概率
                new_log_probs = dist.log_prob(batch_actions)
                
                # 计算比率
                ratio = torch.exp(new_log_probs - batch_old_log_probs)
                
                # 计算策略损失（PPO裁剪）
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - self.clip_range, 1 + self.clip_range) * batch_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # 计算价值损失
                value_loss = F.mse_loss(state_values.squeeze(), batch_returns)
                
                # 计算熵损失（鼓励探索）
                entropy_loss = -dist.entropy().mean()
                
                # 总损失
                loss = policy_loss + self.value_coef * value_loss + self.entropy_coef * entropy_loss
                
                # 反向传播和优化
                self.optimizer.zero_grad()
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.policy.parameters(), self.max_grad_norm)
                
                self.optimizer.step()
                
                # 累积损失
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy_loss += entropy_loss.item()
                update_count += 1
        
        # 清空缓冲区
        self.clear_buffer()
        
        # 返回平均损失
        return {
            'policy_loss': total_policy_loss / update_count,
            'value_loss': total_value_loss / update_count,
            'entropy_loss': total_entropy_loss / update_count
        }
    
    def clear_buffer(self):
        """清空经验缓冲区"""
        self.states = []
        self.actions = []
        self.rewards = []
        self.values = []
        self.log_probs = []
        self.dones = []
    
    def save(self, filepath: str):
        """
        保存模型
        
        Args:
            filepath: 保存路径
        """
        torch.save({
            'policy_state_dict': self.policy.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'gamma': self.gamma,
            'gae_lambda': self.gae_lambda,
            'clip_range': self.clip_range,
            'value_coef': self.value_coef,
            'entropy_coef': self.entropy_coef,
            'max_grad_norm': self.max_grad_norm
        }, filepath)
        logger.info(f"模型已保存到: {filepath}")
    
    def load(self, filepath: str):
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
        """
        checkpoint = torch.load(filepath, map_location=device)
        self.policy.load_state_dict(checkpoint['policy_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.gamma = checkpoint['gamma']
        self.gae_lambda = checkpoint['gae_lambda']
        self.clip_range = checkpoint['clip_range']
        self.value_coef = checkpoint['value_coef']
        self.entropy_coef = checkpoint['entropy_coef']
        self.max_grad_norm = checkpoint['max_grad_norm']
        logger.info(f"模型已从 {filepath} 加载")


class BuyKnowledgeRLAgent(PPOAgent):
    """
    Buy Knowledge RL智能体
    专门用于买入决策
    """
    
    def __init__(self):
        """初始化Buy Knowledge RL智能体"""
        super().__init__(
            input_dim=BUY_KNOWLEDGE_RL_NETWORK['input_dim'],
            hidden_layers=BUY_KNOWLEDGE_RL_NETWORK['hidden_layers'],
            output_dim=BUY_KNOWLEDGE_RL_NETWORK['output_dim'],
            learning_rate=PPO_CONFIG['learning_rate'],
            gamma=PPO_CONFIG['gamma'],
            gae_lambda=PPO_CONFIG['gae_lambda'],
            clip_range=PPO_CONFIG['clip_range'],
            value_coef=PPO_CONFIG['vf_coef'],
            entropy_coef=PPO_CONFIG['ent_coef'],
            max_grad_norm=PPO_CONFIG['max_grad_norm']
        )
        logger.info("Buy Knowledge RL智能体初始化完成")
    
    def predict_buy_probability(self, state: np.ndarray) -> float:
        """
        预测买入概率（收益>=10%的概率）
        
        Args:
            state: 69维状态向量
        
        Returns:
            买入概率
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(device)
        
        with torch.no_grad():
            action_probs, _ = self.policy(state_tensor)
            # 动作1表示预测收益>=10%
            buy_probability = action_probs[0, 1].item()
        
        return buy_probability


class SellKnowledgeRLAgent(PPOAgent):
    """
    Sell Knowledge RL智能体
    专门用于卖出决策
    """
    
    def __init__(self):
        """初始化Sell Knowledge RL智能体"""
        super().__init__(
            input_dim=SELL_KNOWLEDGE_RL_NETWORK['input_dim'],
            hidden_layers=SELL_KNOWLEDGE_RL_NETWORK['hidden_layers'],
            output_dim=SELL_KNOWLEDGE_RL_NETWORK['output_dim'],
            learning_rate=PPO_CONFIG['learning_rate'],
            gamma=PPO_CONFIG['gamma'],
            gae_lambda=PPO_CONFIG['gae_lambda'],
            clip_range=PPO_CONFIG['clip_range'],
            value_coef=PPO_CONFIG['vf_coef'],
            entropy_coef=PPO_CONFIG['ent_coef'],
            max_grad_norm=PPO_CONFIG['max_grad_norm']
        )
        logger.info("Sell Knowledge RL智能体初始化完成")
    
    def predict_sell_probability(self, state: np.ndarray) -> Tuple[float, float]:
        """
        预测卖出概率
        
        Args:
            state: 70维状态向量
        
        Returns:
            (持有概率, 卖出概率)
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(device)
        
        with torch.no_grad():
            action_probs, _ = self.policy(state_tensor)
            hold_probability = action_probs[0, 0].item()
            sell_probability = action_probs[0, 1].item()
        
        return hold_probability, sell_probability
    
    def should_sell(self, state: np.ndarray, threshold: float = 0.85) -> bool:
        """
        判断是否应该卖出
        根据论文，当卖出概率与持有概率的差值超过0.85且卖出概率更高时卖出
        
        Args:
            state: 70维状态向量
            threshold: 决策阈值
        
        Returns:
            是否卖出
        """
        hold_prob, sell_prob = self.predict_sell_probability(state)
        
        # 根据论文的决策规则
        if sell_prob - hold_prob > threshold and sell_prob > hold_prob:
            return True
        
        return False


# 测试代码
if __name__ == "__main__":
    # 创建Buy Knowledge RL智能体
    buy_agent = BuyKnowledgeRLAgent()
    
    # 测试预测
    test_state = np.random.randn(69)
    buy_prob = buy_agent.predict_buy_probability(test_state)
    print(f"Buy Knowledge RL - 买入概率: {buy_prob:.4f}")
    
    # 创建Sell Knowledge RL智能体
    sell_agent = SellKnowledgeRLAgent()
    
    # 测试预测
    test_state = np.random.randn(70)
    hold_prob, sell_prob = sell_agent.predict_sell_probability(test_state)
    print(f"Sell Knowledge RL - 持有概率: {hold_prob:.4f}, 卖出概率: {sell_prob:.4f}")
    
    # 测试卖出决策
    should_sell = sell_agent.should_sell(test_state)
    print(f"是否卖出: {should_sell}")