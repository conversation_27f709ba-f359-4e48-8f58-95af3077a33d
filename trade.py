"""
Pro Trader RL 交易执行脚本
整合Buy Knowledge RL、Sell Knowledge RL和Stop Loss Rule进行实际交易
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import json

# 导入自定义模块
from config import *
from data_preprocessing import DataPreprocessor
from ppo_agent import BuyKnowledgeRLAgent, SellKnowledgeRLAgent
from rl_environments import StopLossRule

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class Position:
    """持仓信息"""
    ts_code: str  # 股票代码
    entry_date: pd.Timestamp  # 入场日期
    entry_price: float  # 入场价格
    shares: int  # 股票数量
    current_price: float  # 当前价格
    current_return: float  # 当前收益率
    holding_days: int  # 持有天数
    stop_loss_triggered: bool = False  # 是否触发止损
    stop_loss_reason: str = ""  # 止损原因


@dataclass
class TradeRecord:
    """交易记录"""
    ts_code: str  # 股票代码
    entry_date: pd.Timestamp  # 入场日期
    exit_date: pd.Timestamp  # 出场日期
    entry_price: float  # 入场价格
    exit_price: float  # 出场价格
    shares: int  # 股票数量
    return_rate: float  # 收益率
    profit_loss: float  # 盈亏金额
    holding_days: int  # 持有天数
    exit_reason: str  # 出场原因（sell_signal, stop_loss, timeout）
    commission: float  # 手续费


class ProTraderRL:
    """
    Pro Trader RL交易系统
    整合所有模块执行交易
    """
    
    def __init__(self, 
                 initial_capital: float = 10000,
                 max_positions: int = 10,
                 commission_rate: float = 0.001):
        """
        初始化交易系统
        
        Args:
            initial_capital: 初始资金
            max_positions: 最大持仓数
            commission_rate: 手续费率
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.max_positions = max_positions
        self.commission_rate = commission_rate
        
        # 加载模型
        self._load_models()
        
        # 加载数据
        self._load_data()
        
        # 初始化交易状态
        self.positions: Dict[str, Position] = {}  # 当前持仓
        self.trade_history: List[TradeRecord] = []  # 交易历史
        self.daily_portfolio_value: List[Dict] = []  # 每日组合价值
        
        # 止损规则
        self.stop_loss_rule = StopLossRule(
            stop_loss_on_dips=STOP_LOSS_ON_DIPS,
            stop_loss_on_sideways_days=STOP_LOSS_ON_SIDEWAYS_DAYS,
            stop_loss_on_sideways_threshold=STOP_LOSS_ON_SIDEWAYS_THRESHOLD
        )
        
        logger.info(f"Pro Trader RL交易系统初始化完成")
        logger.info(f"初始资金: ${initial_capital:,.2f}")
        logger.info(f"最大持仓数: {max_positions}")
        logger.info(f"手续费率: {commission_rate*100:.2f}%")
    
    def _load_models(self):
        """加载训练好的模型"""
        # 加载Buy Knowledge RL模型
        self.buy_agent = BuyKnowledgeRLAgent()
        buy_model_path = os.path.join(MODEL_DIR, 'buy_knowledge_rl_best.pth')
        if os.path.exists(buy_model_path):
            self.buy_agent.load(buy_model_path)
            logger.info(f"已加载Buy Knowledge RL模型: {buy_model_path}")
        else:
            logger.warning("未找到Buy Knowledge RL模型，使用随机初始化")
        
        # 加载Sell Knowledge RL模型
        self.sell_agent = SellKnowledgeRLAgent()
        sell_model_path = os.path.join(MODEL_DIR, 'sell_knowledge_rl_best.pth')
        if os.path.exists(sell_model_path):
            self.sell_agent.load(sell_model_path)
            logger.info(f"已加载Sell Knowledge RL模型: {sell_model_path}")
        else:
            logger.warning("未找到Sell Knowledge RL模型，使用随机初始化")
    
    def _load_data(self):
        """加载和处理数据"""
        logger.info("加载交易数据...")
        
        # 创建数据预处理器
        self.preprocessor = DataPreprocessor()
        
        # 处理数据
        self.data = self.preprocessor.process_all_data()
        
        # 按日期排序
        self.data = self.data.sort_values(['trade_date', 'ts_code']).reset_index(drop=True)
        
        # 获取所有交易日期
        self.trading_dates = sorted(self.data['trade_date'].unique())
        
        logger.info(f"数据加载完成，共 {len(self.data)} 条记录，{len(self.trading_dates)} 个交易日")
    
    def get_buy_signals(self, date: pd.Timestamp) -> List[Dict]:
        """
        获取指定日期的买入信号
        
        Args:
            date: 交易日期
        
        Returns:
            买入信号列表
        """
        # 获取当天有买入信号的股票
        day_data = self.data[
            (self.data['trade_date'] == date) & 
            (self.data['buy_signal'] == True)
        ]
        
        if len(day_data) == 0:
            return []
        
        buy_signals = []
        
        for _, row in day_data.iterrows():
            # 获取69维特征
            features = np.array(row['rl_features'])
            
            # 使用Buy Knowledge RL预测买入概率
            buy_probability = self.buy_agent.predict_buy_probability(features)
            
            buy_signals.append({
                'ts_code': row['ts_code'],
                'buy_probability': buy_probability,
                'price': row['close'],
                'features': features
            })
        
        # 按买入概率排序（论文中提到选择概率最高的前10个）
        buy_signals.sort(key=lambda x: x['buy_probability'], reverse=True)
        
        return buy_signals[:10]  # 最多返回10个信号
    
    def check_sell_signals(self, date: pd.Timestamp) -> List[Tuple[str, str, float]]:
        """
        检查当前持仓的卖出信号
        
        Args:
            date: 交易日期
        
        Returns:
            卖出信号列表 [(股票代码, 卖出原因, 卖出价格)]
        """
        sell_signals = []
        
        for ts_code, position in list(self.positions.items()):
            # 获取当天该股票的数据
            stock_data = self.data[
                (self.data['ts_code'] == ts_code) & 
                (self.data['trade_date'] == date)
            ]
            
            if len(stock_data) == 0:
                continue
            
            row = stock_data.iloc[0]
            current_price = row['close']
            
            # 更新持仓信息
            position.current_price = current_price
            position.current_return = (current_price - position.entry_price) / position.entry_price
            position.holding_days = (date - position.entry_date).days
            
            # 1. 检查止损规则
            returns_history = self._get_returns_history(ts_code, position.entry_date, date)
            should_stop, stop_reason = self.stop_loss_rule.check_stop_loss(
                returns_history, position.current_return
            )
            
            if should_stop:
                sell_signals.append((ts_code, f"stop_loss_{stop_reason}", current_price))
                continue
            
            # 2. 使用Sell Knowledge RL判断
            # 准备70维特征（69维基础特征 + 当前收益率）
            features = np.array(row['rl_features'])
            sell_features = np.concatenate([features, [position.current_return]])
            
            # 判断是否卖出
            should_sell = self.sell_agent.should_sell(sell_features, threshold=0.85)
            
            if should_sell:
                sell_signals.append((ts_code, "sell_signal", current_price))
                continue
            
            # 3. 检查是否超过120天
            if position.holding_days >= 120:
                sell_signals.append((ts_code, "timeout", current_price))
        
        return sell_signals
    
    def _get_returns_history(self, ts_code: str, entry_date: pd.Timestamp, 
                            current_date: pd.Timestamp) -> List[float]:
        """获取历史收益率序列"""
        position = self.positions[ts_code]
        
        # 获取期间的所有数据
        period_data = self.data[
            (self.data['ts_code'] == ts_code) &
            (self.data['trade_date'] > entry_date) &
            (self.data['trade_date'] <= current_date)
        ].sort_values('trade_date')
        
        returns = []
        for _, row in period_data.iterrows():
            ret = (row['close'] - position.entry_price) / position.entry_price
            returns.append(ret)
        
        return returns
    
    def execute_buy(self, ts_code: str, price: float, date: pd.Timestamp) -> bool:
        """
        执行买入
        
        Args:
            ts_code: 股票代码
            price: 买入价格
            date: 买入日期
        
        Returns:
            是否成功买入
        """
        # 检查是否已达到最大持仓数
        if len(self.positions) >= self.max_positions:
            return False
        
        # 检查是否已持有该股票
        if ts_code in self.positions:
            return False
        
        # 计算可买入的股数（每个持仓最多占10%资金）
        max_position_value = self.capital * 0.1
        shares = int(max_position_value / price)
        
        if shares == 0:
            return False
        
        # 计算总成本（包括手续费）
        total_cost = shares * price * (1 + self.commission_rate)
        
        # 检查资金是否充足
        if total_cost > self.capital:
            shares = int(self.capital / (price * (1 + self.commission_rate)))
            if shares == 0:
                return False
            total_cost = shares * price * (1 + self.commission_rate)
        
        # 执行买入
        self.capital -= total_cost
        
        # 创建持仓
        position = Position(
            ts_code=ts_code,
            entry_date=date,
            entry_price=price,
            shares=shares,
            current_price=price,
            current_return=0,
            holding_days=0
        )
        
        self.positions[ts_code] = position
        
        logger.debug(f"买入 {ts_code}: {shares}股 @ ${price:.2f}, 成本${total_cost:.2f}")
        
        return True
    
    def execute_sell(self, ts_code: str, price: float, date: pd.Timestamp, 
                    reason: str) -> bool:
        """
        执行卖出
        
        Args:
            ts_code: 股票代码
            price: 卖出价格
            date: 卖出日期
            reason: 卖出原因
        
        Returns:
            是否成功卖出
        """
        if ts_code not in self.positions:
            return False
        
        position = self.positions[ts_code]
        
        # 计算收益
        gross_proceeds = position.shares * price
        commission = gross_proceeds * self.commission_rate
        net_proceeds = gross_proceeds - commission
        
        # 计算盈亏
        cost_basis = position.shares * position.entry_price * (1 + self.commission_rate)
        profit_loss = net_proceeds - cost_basis
        return_rate = (net_proceeds - cost_basis) / cost_basis
        
        # 更新资金
        self.capital += net_proceeds
        
        # 记录交易
        trade = TradeRecord(
            ts_code=ts_code,
            entry_date=position.entry_date,
            exit_date=date,
            entry_price=position.entry_price,
            exit_price=price,
            shares=position.shares,
            return_rate=return_rate,
            profit_loss=profit_loss,
            holding_days=(date - position.entry_date).days,
            exit_reason=reason,
            commission=commission
        )
        
        self.trade_history.append(trade)
        
        # 移除持仓
        del self.positions[ts_code]
        
        logger.debug(f"卖出 {ts_code}: {position.shares}股 @ ${price:.2f}, "
                    f"盈亏${profit_loss:.2f} ({return_rate*100:.2f}%), 原因:{reason}")
        
        return True
    
    def run_backtest(self, start_date: str = None, end_date: str = None):
        """
        运行回测
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        """
        # 设置日期范围
        if start_date:
            start_date = pd.to_datetime(start_date)
        else:
            start_date = pd.to_datetime(TEST_START_DATE)
        
        if end_date:
            end_date = pd.to_datetime(end_date)
        else:
            end_date = pd.to_datetime(TEST_END_DATE)
        
        # 筛选交易日期
        test_dates = [d for d in self.trading_dates if start_date <= d <= end_date]
        
        logger.info(f"开始回测: {start_date.date()} 到 {end_date.date()}")
        logger.info(f"共 {len(test_dates)} 个交易日")
        
        # 遍历每个交易日
        for date in test_dates:
            # 检查卖出信号
            sell_signals = self.check_sell_signals(date)
            for ts_code, reason, price in sell_signals:
                self.execute_sell(ts_code, price, date, reason)
            
            # 获取买入信号
            buy_signals = self.get_buy_signals(date)
            for signal in buy_signals:
                # 只有当有空余持仓位且买入概率足够高时才买入
                if len(self.positions) < self.max_positions and signal['buy_probability'] > 0.5:
                    self.execute_buy(signal['ts_code'], signal['price'], date)
            
            # 记录每日组合价值
            portfolio_value = self.calculate_portfolio_value(date)
            self.daily_portfolio_value.append({
                'date': date,
                'capital': self.capital,
                'positions_value': portfolio_value - self.capital,
                'total_value': portfolio_value,
                'positions_count': len(self.positions)
            })
        
        # 清仓剩余持仓
        final_date = test_dates[-1]
        for ts_code in list(self.positions.keys()):
            position = self.positions[ts_code]
            self.execute_sell(ts_code, position.current_price, final_date, "backtest_end")
        
        logger.info("回测完成")
    
    def calculate_portfolio_value(self, date: pd.Timestamp) -> float:
        """计算组合总价值"""
        positions_value = 0
        
        for ts_code, position in self.positions.items():
            # 获取当前价格
            stock_data = self.data[
                (self.data['ts_code'] == ts_code) & 
                (self.data['trade_date'] == date)
            ]
            
            if len(stock_data) > 0:
                current_price = stock_data.iloc[0]['close']
                positions_value += position.shares * current_price
            else:
                # 如果没有当天数据，使用最近的价格
                positions_value += position.shares * position.current_price
        
        return self.capital + positions_value
    
    def calculate_metrics(self) -> Dict:
        """计算性能指标"""
        if len(self.trade_history) == 0:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'average_return': 0,
                'sharpe_ratio': 0,
                'max_drawdown': 0,
                'total_return': 0,
                'annual_return': 0
            }
        
        # 基础统计
        total_trades = len(self.trade_history)
        winning_trades = sum(1 for t in self.trade_history if t.profit_loss > 0)
        win_rate = winning_trades / total_trades
        
        # 收益率统计
        returns = [t.return_rate for t in self.trade_history]
        average_return = np.mean(returns)
        std_return = np.std(returns)
        
        # 夏普比率（假设无风险利率为0）
        sharpe_ratio = average_return / std_return if std_return > 0 else 0
        
        # 最大回撤
        portfolio_values = [d['total_value'] for d in self.daily_portfolio_value]
        if len(portfolio_values) > 0:
            cummax = np.maximum.accumulate(portfolio_values)
            drawdown = (portfolio_values - cummax) / cummax
            max_drawdown = np.min(drawdown)
        else:
            max_drawdown = 0
        
        # 总收益率
        final_value = self.calculate_portfolio_value(self.trading_dates[-1])
        total_return = (final_value - self.initial_capital) / self.initial_capital
        
        # 年化收益率
        if len(self.daily_portfolio_value) > 1:
            days = (self.daily_portfolio_value[-1]['date'] - self.daily_portfolio_value[0]['date']).days
            annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        else:
            annual_return = 0
        
        # 交易统计
        avg_holding_days = np.mean([t.holding_days for t in self.trade_history])
        avg_profit = np.mean([t.profit_loss for t in self.trade_history if t.profit_loss > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean([t.profit_loss for t in self.trade_history if t.profit_loss < 0]) if winning_trades < total_trades else 0
        profit_loss_ratio = abs(avg_profit / avg_loss) if avg_loss != 0 else 0
        
        # 按退出原因统计
        exit_reasons = {}
        for trade in self.trade_history:
            reason = trade.exit_reason
            if reason not in exit_reasons:
                exit_reasons[reason] = 0
            exit_reasons[reason] += 1
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades,
            'win_rate': win_rate * 100,
            'average_return': average_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown * 100,
            'total_return': total_return * 100,
            'annual_return': annual_return * 100,
            'avg_holding_days': avg_holding_days,
            'profit_loss_ratio': profit_loss_ratio,
            'exit_reasons': exit_reasons,
            'final_capital': final_value
        }
    
    def save_results(self, filepath: str = None):
        """保存交易结果"""
        if filepath is None:
            filepath = os.path.join(RESULTS_DIR, 
                                   f'trading_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        # 准备保存数据
        results = {
            'metrics': self.calculate_metrics(),
            'trades': [
                {
                    'ts_code': t.ts_code,
                    'entry_date': t.entry_date.strftime('%Y-%m-%d'),
                    'exit_date': t.exit_date.strftime('%Y-%m-%d'),
                    'entry_price': t.entry_price,
                    'exit_price': t.exit_price,
                    'shares': t.shares,
                    'return_rate': t.return_rate,
                    'profit_loss': t.profit_loss,
                    'holding_days': t.holding_days,
                    'exit_reason': t.exit_reason
                }
                for t in self.trade_history
            ],
            'daily_portfolio': [
                {
                    'date': d['date'].strftime('%Y-%m-%d'),
                    'capital': d['capital'],
                    'positions_value': d['positions_value'],
                    'total_value': d['total_value'],
                    'positions_count': d['positions_count']
                }
                for d in self.daily_portfolio_value
            ]
        }
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=4, default=str)
        
        logger.info(f"交易结果已保存到: {filepath}")
    
    def print_summary(self):
        """打印交易摘要"""
        metrics = self.calculate_metrics()
        
        print("\n" + "="*60)
        print("Pro Trader RL 交易摘要")
        print("="*60)
        print(f"初始资金: ${self.initial_capital:,.2f}")
        print(f"最终资金: ${metrics['final_capital']:,.2f}")
        print(f"总收益率: {metrics['total_return']:.2f}%")
        print(f"年化收益率: {metrics['annual_return']:.2f}%")
        print(f"夏普比率: {metrics['sharpe_ratio']:.3f}")
        print(f"最大回撤: {metrics['max_drawdown']:.2f}%")
        print("-"*60)
        print(f"总交易次数: {metrics['total_trades']}")
        print(f"获利交易: {metrics['winning_trades']}")
        print(f"亏损交易: {metrics['losing_trades']}")
        print(f"胜率: {metrics['win_rate']:.2f}%")
        print(f"平均收益率: {metrics['average_return']:.2f}%")
        print(f"平均持有天数: {metrics['avg_holding_days']:.1f}")
        print(f"盈亏比: {metrics['profit_loss_ratio']:.2f}")
        print("-"*60)
        print("退出原因统计:")
        for reason, count in metrics['exit_reasons'].items():
            print(f"  {reason}: {count}")
        print("="*60)


def main():
    """主函数"""
    logger.info("="*60)
    logger.info("Pro Trader RL 交易系统启动")
    logger.info("="*60)
    
    # 创建交易系统
    trader = ProTraderRL(
        initial_capital=BACKTEST_CONFIG['initial_capital'],
        max_positions=BACKTEST_CONFIG['max_stocks'],
        commission_rate=BACKTEST_CONFIG['commission_rate']
    )
    
    # 运行回测
    trader.run_backtest(
        start_date=TEST_START_DATE,
        end_date=TEST_END_DATE
    )
    
    # 打印结果
    trader.print_summary()
    
    # 保存结果
    trader.save_results()
    
    logger.info("交易系统运行完成")


if __name__ == "__main__":
    main()