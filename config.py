"""
Pro Trader RL 配置文件
基于论文: Reinforcement learning framework for generating trading knowledge 
by mimicking the decision-making patterns of professional traders
"""

import os
from datetime import datetime

# ==================== 系统配置 ====================
# Python环境路径
PYTHON_PATH = r"D:\ProgramData\miniconda3\envs\v8new\python.exe"

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 数据目录
DATA_DIR = os.path.join(PROJECT_ROOT, "tushare_data_cyb")

# 模型保存目录
MODEL_DIR = os.path.join(PROJECT_ROOT, "models")
os.makedirs(MODEL_DIR, exist_ok=True)

# 日志目录
LOG_DIR = os.path.join(PROJECT_ROOT, "logs")
os.makedirs(LOG_DIR, exist_ok=True)

# 结果目录
RESULTS_DIR = os.path.join(PROJECT_ROOT, "results")
os.makedirs(RESULTS_DIR, exist_ok=True)

# ==================== 数据配置 ====================
# 数据文件路径
STOCK_BASIC_FILE = os.path.join(DATA_DIR, "stock_basic_cyb.csv")
STOCK_DAILY_FILE = os.path.join(DATA_DIR, "stock_daily_cyb.csv")
DAILY_BASIC_FILE = os.path.join(DATA_DIR, "daily_basic_cyb.csv")
INDEX_DAILY_FILE = os.path.join(DATA_DIR, "index_daily_cyb.csv")
FACTORS_FILE = os.path.join(DATA_DIR, "stock_factors_cyb.csv")

# 数据日期范围
TRAIN_START_DATE = "20210101"  # 训练开始日期
TRAIN_END_DATE = "20221231"    # 训练结束日期
VAL_START_DATE = "20230101"    # 验证开始日期
VAL_END_DATE = "20230531"      # 验证结束日期
TEST_START_DATE = "20230601"   # 测试开始日期
TEST_END_DATE = "20231231"     # 测试结束日期


# ==================== 强化学习配置 ====================
# PPO算法超参数（论文第10页表6）
PPO_CONFIG = {
    'learning_rate': 0.0001,  # 学习率
    'n_steps': 2048,  # 每次更新的步数
    'batch_size': 64,  # 批次大小
    'n_epochs': 10,  # 每次更新的epoch数
    'gamma': 0.99,  # 折扣因子
    'gae_lambda': 0.95,  # GAE lambda
    'clip_range': 0.2,  # PPO裁剪范围
    'clip_range_vf': None,  # 价值函数裁剪范围
    'ent_coef': 0.01,  # 熵系数
    'vf_coef': 0.5,  # 价值函数系数
    'max_grad_norm': 0.5,  # 梯度裁剪
    'target_kl': None,  # 目标KL散度
    'tensorboard_log': LOG_DIR,  # TensorBoard日志目录
    'verbose': 1  # 详细程度
}

# Buy Knowledge RL网络结构（论文第7页）
BUY_KNOWLEDGE_RL_NETWORK = {
    'input_dim': 69,  # 输入维度（归一化后的69个特征）
    'hidden_layers': [69, 40, 2],  # 隐藏层结构
    'activation': 'relu',  # 激活函数
    'output_dim': 2  # 输出维度（2个动作概率）
}

# Sell Knowledge RL网络结构（论文第8页）
SELL_KNOWLEDGE_RL_NETWORK = {
    'input_dim': 70,  # 输入维度（69个特征 + 当前收益率）
    'hidden_layers': [70, 40, 2],  # 隐藏层结构
    'activation': 'relu',  # 激活函数
    'output_dim': 2  # 输出维度（卖出/持有概率）
}

# 训练配置
TRAINING_CONFIG = {
    'total_timesteps': 10000,  # 总训练步数
    'eval_freq': 100,  # 评估频率
    'save_freq': 500,  # 模型保存频率
    'log_interval': 10,  # 日志间隔
    'early_stop_patience': 10,  # 早停耐心值
    'n_eval_episodes': 10,  # 评估回合数
}


'''
TRAINING_CONFIG = {
    'total_timesteps': 1000000,  # 总训练步数
    'eval_freq': 10000,  # 评估频率
    'save_freq': 50000,  # 模型保存频率
    'log_interval': 100,  # 日志间隔
    'early_stop_patience': 10,  # 早停耐心值
    'n_eval_episodes': 10,  # 评估回合数
}
'''




# ==================== 回测配置 ====================
BACKTEST_CONFIG = {
    'initial_capital': 10000,  # 初始资金（美元）
    'max_stocks': 10,  # 最大持股数量
    'max_position_per_stock': 0.1,  # 单只股票最大仓位（10%）
    'commission_rate': 0.001,  # 交易费率（0.1%）
    'slippage': 0.001,  # 滑点
    'min_trade_amount': 100,  # 最小交易金额
}



# ==================== 随机种子配置 ====================
RANDOM_SEED = 42  # 随机种子，确保结果可重现

# ==================== GPU配置 ====================
USE_GPU = True  # 是否使用GPU


