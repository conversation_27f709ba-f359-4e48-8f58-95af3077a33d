#!/usr/bin/env python3
"""
检查买入信号数量的脚本
"""

import pandas as pd
import numpy as np

def main():
    # 读取因子数据
    factors_file = 'tushare_data_cyb/stock_factors_cyb.csv'
    print('正在读取因子数据...')
    df = pd.read_csv(factors_file)
    df['trade_date'] = pd.to_datetime(df['trade_date'])

    print(f'原始数据总量: {len(df)} 条')
    print(f'数据日期范围: {df["trade_date"].min()} 至 {df["trade_date"].max()}')
    print(f'股票数量: {df["ts_code"].nunique()} 只')

    # 应用时间过滤 (2021-2023)
    train_start = pd.to_datetime('20210101')
    val_end = pd.to_datetime('20231231')
    filtered_df = df[(df['trade_date'] >= train_start) & (df['trade_date'] <= val_end)]

    print(f'\n时间过滤后数据: {len(filtered_df)} 条')
    print(f'过滤后日期范围: {filtered_df["trade_date"].min()} 至 {filtered_df["trade_date"].max()}')

    # 检查买入信号
    buy_signals = filtered_df[filtered_df['buy_signal'] == True]
    print(f'\n买入信号总数: {len(buy_signals)} 个')

    # 按年份统计买入信号
    for year in [2021, 2022, 2023]:
        year_signals = buy_signals[buy_signals['trade_date'].dt.year == year]
        print(f'{year}年买入信号: {len(year_signals)} 个')

    # 按股票统计买入信号
    signals_per_stock = buy_signals.groupby('ts_code').size()
    print(f'\n平均每只股票买入信号数: {signals_per_stock.mean():.2f}')
    print(f'买入信号最多的股票: {signals_per_stock.max()} 个')
    print(f'有买入信号的股票数量: {len(signals_per_stock)} 只')

    # 训练集和验证集划分
    train_end = pd.to_datetime('20221231')
    val_start = pd.to_datetime('20230101')

    train_signals = buy_signals[buy_signals['trade_date'] <= train_end]
    val_signals = buy_signals[buy_signals['trade_date'] >= val_start]

    print(f'\n训练集买入信号: {len(train_signals)} 个')
    print(f'验证集买入信号: {len(val_signals)} 个')

    # 检查买入信号的分布
    print(f'\n买入信号分布统计:')
    print(f'buy_signal列的唯一值: {filtered_df["buy_signal"].unique()}')
    print(f'buy_signal为True的数量: {len(filtered_df[filtered_df["buy_signal"] == True])}')
    print(f'buy_signal为False的数量: {len(filtered_df[filtered_df["buy_signal"] == False])}')
    
    # 检查一些具体的买入信号样本
    if len(buy_signals) > 0:
        print(f'\n前5个买入信号样本:')
        sample_signals = buy_signals.head(5)[['ts_code', 'trade_date', 'close', 'high', 'buy_signal']]
        print(sample_signals.to_string())

if __name__ == "__main__":
    main()
