"""
Pro Trader RL 回测评估脚本
对比Pro Trader RL与其他策略的性能
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging
import json
from typing import Dict, List, Tuple

# 导入自定义模块
from config import *
from data_preprocessing import DataPreprocessor
from trade import ProTraderRL

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 设置绘图风格
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")


class BacktestEvaluator:
    """
    回测评估器
    比较Pro Trader RL与基准策略的性能
    """
    
    def __init__(self):
        """初始化评估器"""
        logger.info("初始化回测评估器...")
        
        # 加载数据
        self.preprocessor = DataPreprocessor()
        self.data = self.preprocessor.process_all_data()
        
        # 结果存储
        self.results = {}
        
    def run_pro_trader_rl(self, start_date: str, end_date: str) -> Dict:
        """
        运行Pro Trader RL策略
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            策略结果
        """
        logger.info("运行Pro Trader RL策略...")
        
        trader = ProTraderRL(
            initial_capital=BACKTEST_CONFIG['initial_capital'],
            max_positions=BACKTEST_CONFIG['max_stocks'],
            commission_rate=BACKTEST_CONFIG['commission_rate']
        )
        
        trader.run_backtest(start_date, end_date)
        
        metrics = trader.calculate_metrics()
        
        # 提取每日收益
        daily_returns = []
        portfolio_values = [d['total_value'] for d in trader.daily_portfolio_value]
        
        for i in range(1, len(portfolio_values)):
            daily_return = (portfolio_values[i] - portfolio_values[i-1]) / portfolio_values[i-1]
            daily_returns.append(daily_return)
        
        return {
            'metrics': metrics,
            'daily_returns': daily_returns,
            'portfolio_values': portfolio_values,
            'trades': trader.trade_history,
            'daily_data': trader.daily_portfolio_value
        }
    
    def run_buy_and_hold(self, start_date: str, end_date: str) -> Dict:
        """
        运行买入持有策略（基准）
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            策略结果
        """
        logger.info("运行买入持有策略...")
        
        # 选择市值最大的前10只股票
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        # 获取开始日期的数据
        start_data = self.data[self.data['trade_date'] == start_dt]
        
        if len(start_data) == 0:
            # 找最近的交易日
            available_dates = self.data['trade_date'].unique()
            start_dt = available_dates[available_dates >= start_dt][0]
            start_data = self.data[self.data['trade_date'] == start_dt]
        
        # 按市值排序选择前10只
        if 'total_mv' in start_data.columns:
            top_stocks = start_data.nlargest(10, 'total_mv')['ts_code'].tolist()
        else:
            # 如果没有市值数据，随机选择10只
            top_stocks = start_data['ts_code'].sample(min(10, len(start_data)))['ts_code'].tolist()
        
        # 计算每只股票分配的资金
        capital_per_stock = BACKTEST_CONFIG['initial_capital'] / len(top_stocks)
        
        # 买入并持有
        portfolio = {}
        for ts_code in top_stocks:
            stock_start = self.data[
                (self.data['ts_code'] == ts_code) & 
                (self.data['trade_date'] == start_dt)
            ]
            
            if len(stock_start) > 0:
                buy_price = stock_start.iloc[0]['close']
                shares = int(capital_per_stock / buy_price)
                portfolio[ts_code] = {
                    'shares': shares,
                    'buy_price': buy_price
                }
        
        # 计算每日组合价值
        trading_dates = sorted(self.data[
            (self.data['trade_date'] >= start_dt) & 
            (self.data['trade_date'] <= end_dt)
        ]['trade_date'].unique())
        
        portfolio_values = []
        daily_returns = []
        
        for date in trading_dates:
            total_value = 0
            
            for ts_code, position in portfolio.items():
                stock_data = self.data[
                    (self.data['ts_code'] == ts_code) & 
                    (self.data['trade_date'] == date)
                ]
                
                if len(stock_data) > 0:
                    current_price = stock_data.iloc[0]['close']
                    total_value += position['shares'] * current_price
            
            portfolio_values.append(total_value)
            
            if len(portfolio_values) > 1:
                daily_return = (portfolio_values[-1] - portfolio_values[-2]) / portfolio_values[-2]
                daily_returns.append(daily_return)
        
        # 计算指标
        total_return = (portfolio_values[-1] - BACKTEST_CONFIG['initial_capital']) / BACKTEST_CONFIG['initial_capital']
        
        if len(daily_returns) > 0:
            avg_return = np.mean(daily_returns)
            std_return = np.std(daily_returns)
            sharpe_ratio = avg_return / std_return * np.sqrt(252) if std_return > 0 else 0
            
            # 最大回撤
            cummax = np.maximum.accumulate(portfolio_values)
            drawdown = (portfolio_values - cummax) / cummax
            max_drawdown = np.min(drawdown)
        else:
            sharpe_ratio = 0
            max_drawdown = 0
        
        # 年化收益率
        days = (trading_dates[-1] - trading_dates[0]).days
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        
        return {
            'metrics': {
                'total_return': total_return * 100,
                'annual_return': annual_return * 100,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown * 100,
                'final_capital': portfolio_values[-1]
            },
            'daily_returns': daily_returns,
            'portfolio_values': portfolio_values
        }
    
    def run_donchian_strategy(self, start_date: str, end_date: str) -> Dict:
        """
        运行Donchian Channel策略
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            策略结果
        """
        logger.info("运行Donchian Channel策略...")
        
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        # 筛选时间范围内的数据
        test_data = self.data[
            (self.data['trade_date'] >= start_dt) & 
            (self.data['trade_date'] <= end_dt)
        ].copy()
        
        # 初始化
        capital = BACKTEST_CONFIG['initial_capital']
        positions = {}
        trades = []
        portfolio_values = []
        
        # 按日期分组
        for date, day_data in test_data.groupby('trade_date'):
            # 检查卖出信号
            for ts_code in list(positions.keys()):
                stock_data = day_data[day_data['ts_code'] == ts_code]
                
                if len(stock_data) > 0 and stock_data.iloc[0]['sell_signal']:
                    # 执行卖出
                    position = positions[ts_code]
                    sell_price = stock_data.iloc[0]['close']
                    proceeds = position['shares'] * sell_price * (1 - BACKTEST_CONFIG['commission_rate'])
                    capital += proceeds
                    
                    # 记录交易
                    trades.append({
                        'ts_code': ts_code,
                        'entry_date': position['entry_date'],
                        'exit_date': date,
                        'entry_price': position['entry_price'],
                        'exit_price': sell_price,
                        'shares': position['shares'],
                        'return': (sell_price - position['entry_price']) / position['entry_price']
                    })
                    
                    del positions[ts_code]
            
            # 检查买入信号
            buy_signals = day_data[day_data['buy_signal'] == True]
            
            for _, row in buy_signals.iterrows():
                if len(positions) >= 10:  # 最多持有10只股票
                    break
                
                ts_code = row['ts_code']
                if ts_code in positions:  # 已持有
                    continue
                
                # 执行买入
                buy_price = row['close']
                max_position = capital * 0.1  # 每个仓位最多10%
                shares = int(max_position / buy_price)
                
                if shares > 0:
                    cost = shares * buy_price * (1 + BACKTEST_CONFIG['commission_rate'])
                    if cost <= capital:
                        capital -= cost
                        positions[ts_code] = {
                            'shares': shares,
                            'entry_price': buy_price,
                            'entry_date': date
                        }
            
            # 计算当日组合价值
            positions_value = 0
            for ts_code, position in positions.items():
                stock_data = day_data[day_data['ts_code'] == ts_code]
                if len(stock_data) > 0:
                    current_price = stock_data.iloc[0]['close']
                    positions_value += position['shares'] * current_price
            
            total_value = capital + positions_value
            portfolio_values.append(total_value)
        
        # 计算每日收益
        daily_returns = []
        for i in range(1, len(portfolio_values)):
            daily_return = (portfolio_values[i] - portfolio_values[i-1]) / portfolio_values[i-1]
            daily_returns.append(daily_return)
        
        # 计算指标
        if len(trades) > 0:
            win_rate = sum(1 for t in trades if t['return'] > 0) / len(trades) * 100
            avg_return = np.mean([t['return'] for t in trades]) * 100
        else:
            win_rate = 0
            avg_return = 0
        
        total_return = (portfolio_values[-1] - BACKTEST_CONFIG['initial_capital']) / BACKTEST_CONFIG['initial_capital']
        
        if len(daily_returns) > 0:
            sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(252)
            
            # 最大回撤
            cummax = np.maximum.accumulate(portfolio_values)
            drawdown = (portfolio_values - cummax) / cummax
            max_drawdown = np.min(drawdown)
        else:
            sharpe_ratio = 0
            max_drawdown = 0
        
        # 年化收益率
        days = (end_dt - start_dt).days
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        
        return {
            'metrics': {
                'total_trades': len(trades),
                'win_rate': win_rate,
                'average_return': avg_return,
                'total_return': total_return * 100,
                'annual_return': annual_return * 100,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown * 100,
                'final_capital': portfolio_values[-1]
            },
            'daily_returns': daily_returns,
            'portfolio_values': portfolio_values,
            'trades': trades
        }
    
    def compare_strategies(self, start_date: str, end_date: str):
        """
        比较所有策略
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        """
        logger.info("="*60)
        logger.info("开始策略对比回测")
        logger.info(f"回测期间: {start_date} 到 {end_date}")
        logger.info("="*60)
        
        # 运行各策略
        self.results['pro_trader_rl'] = self.run_pro_trader_rl(start_date, end_date)
        self.results['buy_and_hold'] = self.run_buy_and_hold(start_date, end_date)
        self.results['donchian'] = self.run_donchian_strategy(start_date, end_date)
        
        # 打印对比结果
        self.print_comparison()
        
        # 绘制对比图表
        self.plot_comparison()
        
        # 保存结果
        self.save_results()
    
    def print_comparison(self):
        """打印策略对比结果"""
        print("\n" + "="*80)
        print("策略性能对比")
        print("="*80)
        
        # 准备表格数据
        metrics_table = []
        
        for strategy_name, result in self.results.items():
            metrics = result['metrics']
            row = {
                '策略': strategy_name.replace('_', ' ').title(),
                '总收益率(%)': f"{metrics.get('total_return', 0):.2f}",
                '年化收益率(%)': f"{metrics.get('annual_return', 0):.2f}",
                '夏普比率': f"{metrics.get('sharpe_ratio', 0):.3f}",
                '最大回撤(%)': f"{metrics.get('max_drawdown', 0):.2f}",
                '最终资金($)': f"{metrics.get('final_capital', 0):,.2f}"
            }
            
            # 添加交易统计（如果有）
            if 'total_trades' in metrics:
                row['交易次数'] = metrics['total_trades']
                row['胜率(%)'] = f"{metrics.get('win_rate', 0):.2f}"
            
            metrics_table.append(row)
        
        # 打印表格
        df = pd.DataFrame(metrics_table)
        print(df.to_string(index=False))
        print("="*80)
        
        # 找出最佳策略
        best_return = max(self.results.items(), 
                         key=lambda x: x[1]['metrics'].get('total_return', 0))
        best_sharpe = max(self.results.items(), 
                         key=lambda x: x[1]['metrics'].get('sharpe_ratio', 0))
        best_drawdown = min(self.results.items(), 
                           key=lambda x: abs(x[1]['metrics'].get('max_drawdown', 0)))
        
        print("\n最佳表现:")
        print(f"  最高收益率: {best_return[0].replace('_', ' ').title()}")
        print(f"  最高夏普比率: {best_sharpe[0].replace('_', ' ').title()}")
        print(f"  最小回撤: {best_drawdown[0].replace('_', ' ').title()}")
        print("="*80)
    
    def plot_comparison(self):
        """绘制对比图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 累计收益曲线
        ax1 = axes[0, 0]
        for strategy_name, result in self.results.items():
            portfolio_values = result['portfolio_values']
            returns = [(v / BACKTEST_CONFIG['initial_capital'] - 1) * 100 
                      for v in portfolio_values]
            ax1.plot(returns, label=strategy_name.replace('_', ' ').title(), linewidth=2)
        
        ax1.set_title('Cumulative Returns Comparison', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Trading Days')
        ax1.set_ylabel('Cumulative Return (%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 性能指标对比
        ax2 = axes[0, 1]
        metrics_names = ['total_return', 'annual_return', 'sharpe_ratio']
        metrics_labels = ['Total Return (%)', 'Annual Return (%)', 'Sharpe Ratio']
        
        x = np.arange(len(metrics_names))
        width = 0.25
        
        for i, (strategy_name, result) in enumerate(self.results.items()):
            metrics = result['metrics']
            values = [metrics.get(m, 0) for m in metrics_names]
            ax2.bar(x + i*width, values, width, 
                   label=strategy_name.replace('_', ' ').title())
        
        ax2.set_title('Performance Metrics Comparison', fontsize=14, fontweight='bold')
        ax2.set_xticks(x + width)
        ax2.set_xticklabels(metrics_labels)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 回撤分析
        ax3 = axes[1, 0]
        for strategy_name, result in self.results.items():
            portfolio_values = result['portfolio_values']
            cummax = np.maximum.accumulate(portfolio_values)
            drawdown = (portfolio_values - cummax) / cummax * 100
            ax3.fill_between(range(len(drawdown)), drawdown, 0, 
                            alpha=0.3, label=strategy_name.replace('_', ' ').title())
        
        ax3.set_title('Drawdown Analysis', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Trading Days')
        ax3.set_ylabel('Drawdown (%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 收益分布
        ax4 = axes[1, 1]
        for strategy_name, result in self.results.items():
            if 'daily_returns' in result and len(result['daily_returns']) > 0:
                daily_returns = np.array(result['daily_returns']) * 100
                ax4.hist(daily_returns, bins=30, alpha=0.5, 
                        label=strategy_name.replace('_', ' ').title())
        
        ax4.set_title('Daily Returns Distribution', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Daily Return (%)')
        ax4.set_ylabel('Frequency')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle('Pro Trader RL Strategy Comparison', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存图表
        plot_path = os.path.join(RESULTS_DIR, 
                                f'strategy_comparison_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        logger.info(f"对比图表已保存到: {plot_path}")
    
    def save_results(self):
        """保存回测结果"""
        results_path = os.path.join(RESULTS_DIR, 
                                   f'backtest_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        # 准备保存数据
        save_data = {}
        for strategy_name, result in self.results.items():
            save_data[strategy_name] = {
                'metrics': result['metrics'],
                'portfolio_values': result['portfolio_values'][:100] if len(result['portfolio_values']) > 100 else result['portfolio_values'],  # 只保存前100个值以减少文件大小
                'daily_returns_stats': {
                    'mean': np.mean(result['daily_returns']) if result['daily_returns'] else 0,
                    'std': np.std(result['daily_returns']) if result['daily_returns'] else 0,
                    'min': np.min(result['daily_returns']) if result['daily_returns'] else 0,
                    'max': np.max(result['daily_returns']) if result['daily_returns'] else 0
                }
            }
        
        with open(results_path, 'w') as f:
            json.dump(save_data, f, indent=4, default=str)
        
        logger.info(f"回测结果已保存到: {results_path}")


def main():
    """主函数"""
    logger.info("="*60)
    logger.info("Pro Trader RL 回测评估系统")
    logger.info("="*60)
    
    # 创建评估器
    evaluator = BacktestEvaluator()
    
    # 运行策略对比
    evaluator.compare_strategies(
        start_date=TEST_START_DATE,
        end_date=TEST_END_DATE
    )
    
    logger.info("回测评估完成")


if __name__ == "__main__":
    main()