# Pro Trader RL 依赖包列表
# Python版本要求: >=3.8

# 数据处理
pandas>=1.3.0
numpy>=1.21.0
scipy>=1.7.0
scikit-learn>=1.0.0

# 技术指标计算
talib-binary>=0.4.24  # Windows用户可能需要单独安装TA-Lib
# 或者使用: ta>=0.10.0 作为替代

# 深度学习和强化学习
torch>=1.10.0
gym>=0.21.0
stable-baselines3>=1.5.0

# 数据获取
tushare>=1.2.89
yfinance>=0.1.70  # 备选数据源

# 可视化
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0  # 可选，用于交互式图表

# 并行计算
joblib>=1.0.0
multiprocessing-logging>=0.3.1

# 进度条
tqdm>=4.62.0

# 日志和配置
pyyaml>=5.4.0
python-dotenv>=0.19.0

# 开发和测试（可选）
pytest>=6.2.0
pytest-cov>=2.12.0
black>=21.6b0
flake8>=3.9.0
mypy>=0.910

# Jupyter支持（可选）
jupyter>=1.0.0
ipywidgets>=7.6.0
nbformat>=5.1.0

# 性能优化（可选）
numba>=0.54.0
cython>=0.29.0

# 其他工具
openpyxl>=3.0.0  # Excel文件支持
h5py>=3.0.0  # HDF5文件支持
tables>=3.6.0  # PyTables支持