"""
数据预处理模块
实现Pro Trader RL论文中的所有69个输入变量和数据归一化
主要负责：
1. 读取已计算的因子CSV文件
2. 数据归一化
3. 准备训练数据
"""

import pandas as pd
import numpy as np
from typing import Tuple
import warnings
import os
from config import *
import logging

warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataPreprocessor:
    """
    数据预处理器
    负责：
    1. 读取已计算的因子CSV文件
    2. 数据归一化
    3. 准备训练数据
    """

    def __init__(self, config: dict = None):
        """初始化数据预处理器"""
        self.config = config or {}
        self.processed_data = None
        # 设置因子数据文件路径
        self.factors_file = os.path.join('tushare_data_cyb', 'stock_factors_cyb.csv')

    def load_factors_data(self) -> pd.DataFrame:
        """加载已计算的因子数据"""
        logger.info("加载已计算的因子数据...")

        if not os.path.exists(self.factors_file):
            raise FileNotFoundError(f"因子数据文件不存在: {self.factors_file}")

        # 读取因子数据
        factors_data = pd.read_csv(self.factors_file)
        factors_data['trade_date'] = pd.to_datetime(factors_data['trade_date'])

        logger.info(f"原始数据加载完成：{len(factors_data)} 条记录")
        logger.info(f"原始数据日期范围：{factors_data['trade_date'].min()} 至 {factors_data['trade_date'].max()}")

        # 应用配置文件中的时间过滤
        # 使用训练和验证的完整时间范围
        filter_start = pd.to_datetime(TRAIN_START_DATE)
        filter_end = pd.to_datetime(VAL_END_DATE)  # 包含验证集的结束时间

        # 过滤数据
        factors_data = factors_data[
            (factors_data['trade_date'] >= filter_start) &
            (factors_data['trade_date'] <= filter_end)
        ]

        logger.info(f"时间过滤后数据：{len(factors_data)} 条记录")
        logger.info(f"数据日期范围：{factors_data['trade_date'].min()} 至 {factors_data['trade_date'].max()}")
        logger.info(f"包含股票数量：{factors_data['ts_code'].nunique()} 只")

        return factors_data
    


    
    def prepare_rl_features(self, df: pd.DataFrame) -> np.ndarray:
        """
        准备强化学习的输入特征（69维）
        按照论文要求的顺序组织特征
        """
        feature_columns = []
        
        # 1. 基础变量（9个，但Open和Volume被排除，实际使用7个）
        basic_cols = ['high_norm', 'low_norm', 'close_norm', 
                     'ha_high_norm', 'ha_low_norm', 'ha_close_norm', 'return']
        feature_columns.extend(basic_cols)
        
        # 2. 技术指标变量（21个）
        feature_columns.append('atr_norm')  # 1个
        feature_columns.extend([f'stock_{n}_norm' for n in range(1, 13)])  # 12个
        feature_columns.extend(['super_trend_14', 'super_trend_21'])  # 2个
        feature_columns.extend(['mfi_norm', 'rsi_norm'])  # 2个
        feature_columns.extend(['donchian_upper_norm', 'donchian_lower_norm'])  # 2个
        feature_columns.append('avg_stock_norm')  # 1个
        # 添加缺失的ha_open_norm（论文中的基础变量）
        feature_columns.append('ha_open_norm')  # 1个
        
        # 3. 股票指数变量（13个）
        feature_columns.append('dji_atr_norm')
        feature_columns.extend([f'index_{n}_norm' for n in range(1, 13)])  # 12个
        
        # 4. 股票指数对比变量（28个）
        feature_columns.extend([f'rs_{n}_norm' for n in range(1, 13)])  # 12个
        feature_columns.extend([f'rs_avg_{n}_norm' for n in [2, 4, 6, 8, 10, 12]])  # 6个
        feature_columns.extend([f'rs_rate_{n}_norm' for n in [5, 10, 20, 40]])  # 4个
        feature_columns.extend(['up_stock', 'down_stock'])  # 2个
        # 添加缺失的特征以达到69维
        feature_columns.extend(['open_norm', 'vol_norm'])  # 2个额外特征
        feature_columns.extend(['pe_norm', 'pb_norm'])  # 2个基本面特征
        
        # 确保所有列都存在
        missing_cols = [col for col in feature_columns if col not in df.columns]
        if missing_cols:
            logger.warning(f"缺少以下特征列: {missing_cols}")
            logger.info(f"期望的特征列数量: {len(feature_columns)}")
            logger.info(f"实际存在的特征列数量: {len([col for col in feature_columns if col in df.columns])}")
            # 用0填充缺失列
            for col in missing_cols:
                df[col] = 0
        
        # 提取特征并转换为numpy数组
        features = df[feature_columns].fillna(0).values
        
        # 验证特征维度
        assert features.shape[1] == 69, f"特征维度应为69，实际为{features.shape[1]}"
        
        return features
    
    def process_all_data(self) -> pd.DataFrame:
        """
        处理所有数据的主函数
        直接读取已计算和归一化的因子数据，只需准备特征
        """
        logger.info("开始处理数据...")

        # 1. 加载已计算和归一化的因子数据
        factors_data = self.load_factors_data()

        # 2. 按股票代码分组处理
        processed_dfs = []

        for ts_code, group in factors_data.groupby('ts_code'):
            logger.debug(f"处理股票 {ts_code}...")

            # 确保数据按日期排序
            group = group.sort_values('trade_date').reset_index(drop=True)

            # 跳过数据不足的股票
            if len(group) < 100:
                continue

            # 3. 准备RL特征（数据已经在calculate_stock_factors_test.py中归一化了）
            features = self.prepare_rl_features(group)
            group['rl_features'] = features.tolist()

            processed_dfs.append(group)

        # 合并所有处理后的数据
        self.processed_data = pd.concat(processed_dfs, ignore_index=True)

        logger.info(f"数据处理完成，共处理 {len(self.processed_data)} 条记录")

        return self.processed_data
    
    def save_processed_data(self, filepath: str = None):
        """保存处理后的数据"""
        if filepath is None:
            filepath = os.path.join(DATA_DIR, 'processed_data.csv')
        
        if self.processed_data is not None:
            self.processed_data.to_csv(filepath, index=False)
            logger.info(f"处理后的数据已保存到 {filepath}")
        else:
            logger.warning("没有处理后的数据可保存")
    
    def get_training_data(self, start_date: str = None, end_date: str = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取训练数据
        返回：(特征, 标签)
        """
        if self.processed_data is None:
            self.process_all_data()
        
        # 日期筛选
        if start_date:
            start_date = pd.to_datetime(start_date)
            mask = self.processed_data['trade_date'] >= start_date
            data = self.processed_data[mask]
        else:
            data = self.processed_data
        
        if end_date:
            end_date = pd.to_datetime(end_date)
            data = data[data['trade_date'] <= end_date]
        
        # 只使用有买入信号的数据
        buy_signal_data = data[data['buy_signal'] == True]
        
        # 准备特征和标签
        features = []
        labels = []
        
        for _, row in buy_signal_data.iterrows():
            # 特征：69维向量
            feature = row['rl_features']
            features.append(feature)
            
            # 标签：计算未来收益率是否超过10%
            ts_code = row['ts_code']
            current_date = row['trade_date']
            future_data = self.processed_data[
                (self.processed_data['ts_code'] == ts_code) &
                (self.processed_data['trade_date'] > current_date)
            ].head(120)  # 未来120天
            
            if len(future_data) > 0:
                # 计算最大收益率
                entry_price = row['close']
                max_return = ((future_data['high'].max() - entry_price) / entry_price) * 100
                label = 1 if max_return >= 10 else 0
            else:
                label = 0
            
            labels.append(label)
        
        return np.array(features), np.array(labels)


# 测试代码
if __name__ == "__main__":
    # 创建数据预处理器实例
    preprocessor = DataPreprocessor()

    # 检查因子文件是否存在
    if not os.path.exists(preprocessor.factors_file):
        print(f"错误：因子文件不存在 {preprocessor.factors_file}")
        print("请先运行 tushare_data_cyb/calculate_stock_factors_test.py 生成因子数据")
        exit(1)

    # 处理所有数据（直接读取已计算的因子CSV）
    processed_data = preprocessor.process_all_data()

    # 保存处理后的数据
    preprocessor.save_processed_data()

    # 获取训练数据
    X_train, y_train = preprocessor.get_training_data(
        start_date=TRAIN_START_DATE,
        end_date=TRAIN_END_DATE
    )

    print(f"训练数据形状: X={X_train.shape}, y={y_train.shape}")
    print(f"标签分布: 0={np.sum(y_train==0)}, 1={np.sum(y_train==1)}")
    print("数据预处理完成！现在可以进行模型训练。")