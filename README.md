# Pro Trader RL - 强化学习交易系统

基于论文《Pro Trader RL: Reinforcement learning framework for generating trading knowledge by mimicking the decision-making patterns of professional traders》的完整实现。

## 📋 项目概述

Pro Trader RL是一个创新的强化学习框架，通过模仿专业交易员的决策模式和交易哲学来生成交易知识。该系统整合了四个核心模块：

1. **数据预处理模块** - 计算69个技术指标和特征
2. **Buy Knowledge RL** - 买入决策的强化学习智能体
3. **Sell Knowledge RL** - 卖出决策的强化学习智能体  
4. **Stop Loss Rule** - 风险管理止损规则

## 🚀 快速开始

### 环境要求

- Python 3.8+ (推荐使用 `D:\ProgramData\miniconda3\envs\v8new\python.exe`)
- CUDA 11.0+ (可选，用于GPU加速)

### 安装依赖

```bash
pip install -r requirements.txt
```

### 数据准备

1. 确保以下数据文件在 `tushare_data_cyb/` 目录下：
   - `stock_basic_cyb.csv` - 股票基本信息
   - `stock_daily_cyb.csv` - 股票日线数据
   - `daily_basic_cyb.csv` - 每日基本面数据
   - `index_daily_cyb.csv` - 指数日线数据

2. 运行数据下载脚本（如需更新数据）：
```bash
python download_cyb_new.py
```

3. 计算因子（可选，如需更新）：
```bash
python tushare_data_cyb/calculate_stock_factors_test.py
```

## 📊 使用指南

### 1. 训练模型

```bash
python train.py
```

训练脚本会：
- 处理数据并计算所有69个特征
- 训练Buy Knowledge RL智能体
- 训练Sell Knowledge RL智能体
- 保存模型到 `models/` 目录
- 生成训练曲线和日志

### 2. 执行交易

```bash
python trade.py
```

交易脚本会：
- 加载训练好的模型
- 整合Buy/Sell RL和止损规则
- 执行回测交易
- 输出交易记录和性能指标

### 3. 策略对比评估

```bash
python backtest.py
```

回测脚本会：
- 对比Pro Trader RL与基准策略
- 生成性能对比报告
- 绘制累计收益、回撤等图表

## 📁 项目结构

```
pro_trader_rl/
│
├── config.py                 # 配置文件（所有可调参数）
├── data_preprocessing.py     # 数据预处理（69个特征计算）
├── rl_environments.py        # 强化学习环境（Buy/Sell RL环境）
├── ppo_agent.py             # PPO算法和神经网络
├── train.py                 # 训练脚本
├── trade.py                 # 交易执行脚本
├── backtest.py              # 回测评估脚本
│
├── tushare_data_cyb/        # 数据目录
│   ├── stock_daily_cyb.csv
│   ├── daily_basic_cyb.csv
│   ├── index_daily_cyb.csv
│   └── calculate_stock_factors_test.py
│
├── models/                  # 模型保存目录
├── logs/                    # 日志目录
├── results/                 # 结果目录
│
├── requirements.txt         # 依赖包列表
└── README.md               # 项目说明文档
```

## 🔧 配置说明

主要配置项在 `config.py` 中：

### 数据配置
- `TRAIN_START_DATE` / `TRAIN_END_DATE` - 训练数据范围
- `TEST_START_DATE` / `TEST_END_DATE` - 测试数据范围

### 技术指标（69个特征）
- 基础变量（9个）：OHLC价格、成交量、Heikin-Ashi蜡烛
- 技术指标（21个）：ATR、RSI、MFI、Super Trend、Donchian Channel等
- 指数变量（13个）：DJI ATR、Index对比等
- 股票vs指数（26个）：相对强度、RS Rate、上涨/下跌股票数等

### PPO超参数
```python
PPO_CONFIG = {
    'learning_rate': 0.0001,
    'n_steps': 2048,
    'batch_size': 64,
    'gamma': 0.99,
    'gae_lambda': 0.95,
    'clip_range': 0.2,
    'ent_coef': 0.01,
    'vf_coef': 0.5
}
```

### 网络结构
- Buy Knowledge RL: [69, 40, 2] - 69维输入，2个动作
- Sell Knowledge RL: [70, 40, 2] - 70维输入（+当前收益率），2个动作

### 交易参数
- `initial_capital`: 10000 - 初始资金
- `max_stocks`: 10 - 最大持仓数
- `commission_rate`: 0.001 - 手续费率

### 止损规则
- 下跌止损：-10%
- 横盘止损：20天内收益<-10%

## 📈 性能指标

系统评估以下指标：
- **收益率**：总收益率、年化收益率
- **风险指标**：夏普比率、最大回撤
- **交易统计**：胜率、盈亏比、平均持有天数
- **准确率**：10%以上收益预测准确率

## 🔬 核心算法

### Buy Knowledge RL
- **状态空间**：69维归一化特征向量
- **动作空间**：2个离散动作（预测收益>=10%，预测收益<10%）
- **奖励函数**：基于预测准确性的0/1奖励

### Sell Knowledge RL  
- **状态空间**：70维特征（69维基础+当前收益率）
- **动作空间**：2个离散动作（卖出，持有）
- **奖励函数**：相对收益奖励（最高收益+2，最低+1）

### 决策规则
1. **买入**：选择概率最高的前10个信号
2. **卖出**：当卖出概率-持有概率>0.85时卖出
3. **止损**：触发下跌或横盘止损规则时强制卖出

## 📊 实验结果

根据论文，Pro Trader RL在不同市场条件下都表现优异：

| 市场条件 | 年化收益率 | 夏普比率 | 最大回撤 |
|---------|-----------|---------|---------|
| 横盘市场 | 46.13%    | 2.38    | 13.49%  |
| 熊市     | 55.99%    | 0.99    | 26.33%  |
| 牛市     | 90.33%    | 3.99    | 5.19%   |
| 综合     | 65.28%    | 4.58    | 8.37%   |

## ⚠️ 风险提示

- 本系统仅供研究学习使用
- 历史表现不代表未来收益
- 实际交易需考虑更多因素（滑点、流动性等）
- 建议先在模拟环境充分测试

## 📚 参考文献

```
Jeong, D. W., & Gu, Y. H. (2024). 
Pro Trader RL: Reinforcement learning framework for generating trading 
knowledge by mimicking the decision-making patterns of professional traders. 
Expert Systems With Applications, 254, 124465.
```

## 📧 联系方式

如有问题，请提交Issue或联系项目维护者。

## 📄 许可证

本项目基于学术研究目的开发，使用请遵守相关法律法规。

---

**免责声明**：本项目仅供学术研究使用，不构成投资建议。使用者需自行承担投资风险。